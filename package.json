{"name": "@dohoder/source", "version": "0.0.0", "license": "MIT", "scripts": {"start:app": "nx run @dohoder/dohoder-mobile:start", "start:admin": "nx run @dohoder/dohoder-admin:dev", "start:calendar-api": "nx run @dohoder/calendar-sync-api:serve"}, "private": true, "dependencies": {"@expo/metro-config": "~0.20.14", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^15.0.2", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.26", "@supabase/supabase-js": "^2.58.0", "@tanstack/react-query": "^5.90.2", "expo": "~53.0.10", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.8", "express": "^4.21.2", "next": "~15.2.4", "react": "19.0.0", "react-dom": "19.0.0", "react-is": "19.0.0", "react-native": "0.79.3", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0", "react-native-svg": "~15.11.2", "react-native-svg-transformer": "~1.5.1", "react-native-web": "~0.20.0", "styled-components": "5.3.6"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@eslint/compat": "^1.1.1", "@eslint/eslintrc": "^2.1.1", "@eslint/js": "^9.8.0", "@expo/cli": "~0.24.14", "@next/eslint-plugin-next": "^15.2.4", "@nx/esbuild": "21.5.3", "@nx/eslint": "21.5.3", "@nx/eslint-plugin": "21.5.3", "@nx/expo": "^21.5.3", "@nx/jest": "21.5.3", "@nx/js": "21.5.3", "@nx/next": "^21.5.3", "@nx/node": "^21.5.3", "@nx/react-native": "^21.5.3", "@nx/vite": "21.5.3", "@nx/web": "21.5.3", "@react-native-community/cli": "~18.0.0", "@react-native-community/cli-platform-android": "~18.0.0", "@react-native-community/cli-platform-ios": "~18.0.0", "@react-native/babel-preset": "~0.79.3", "@react-native/metro-config": "~0.79.3", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@swc/plugin-styled-components": "^1.5.67", "@testing-library/react-native": "~13.2.0", "@types/express": "^4.17.21", "@types/jest": "^30.0.0", "@types/node": "20.19.9", "@types/react": "~19.0.10", "@types/react-dom": "19.0.0", "@types/react-is": "19.0.0", "@types/styled-components": "5.1.26", "@vitest/coverage-v8": "^3.0.5", "@vitest/ui": "^3.0.0", "babel-jest": "^30.0.2", "babel-plugin-styled-components": "1.10.7", "babel-preset-expo": "~13.2.0", "dotenv": "^17.2.2", "esbuild": "^0.19.2", "eslint": "^9.8.0", "eslint-config-next": "^15.2.4", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "jest-util": "^30.0.2", "jiti": "2.4.2", "jsonc-eslint-parser": "^2.1.0", "metro-config": "~0.82.4", "metro-resolver": "~0.82.4", "nx": "21.5.3", "prettier": "^2.6.2", "react-test-renderer": "~19.0.0", "ts-jest": "^29.4.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.9.2", "typescript-eslint": "^8.40.0", "vite": "^7.0.0", "vitest": "^3.0.0"}, "workspaces": ["packages/*", "apps/*", "libs/*"]}