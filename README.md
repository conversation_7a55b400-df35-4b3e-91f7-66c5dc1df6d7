# DOHODER Monorepo

## 🚀 Prehľad

Tento repozitár je monolitické úložisko spravované pomocou **NX**, ktoré združuje všetky aplikácie a zdieľané knižnice pre projekt **DOHODER**.

Projekt je zameraný na mobilné riešenie **Mobile-First** s Next.js administráciou a Node.js API pre kritické úlohy. Ako dátová vrstva slúži **Supabase**.

---

## ⚙️ Rýchla Inicializácia (Setup)

### 1. Inštalácia Závislostí

Spustite inštaláciu všetkých závislostí:

```bash
npm install
```

### 2. Konfigurácia Prostredia

Vytvorte súbor .env v koreňovom adresári a doplňte URL a kľúč pre Supabase:

# .env (Vytvorte v koreni projektu)

```SUPABASE_URL="https://[VASE_SUPABASE_ID].supabase.co"
SUPABASE_ANON_KEY="[VASE_ANON_KEY]"
```
