image: node:20

clone:
  depth: full

pipelines:
  pull-requests:
    '**':
      - step:
          name: 'Build and test affected apps on Pull Requests'
          script:
            - export NX_BRANCH=$BITBUCKET_PR_ID

            # This enables task distribution via Nx Cloud
            # Run this command as early as possible, before dependencies are installed
            # Learn more at https://nx.dev/ci/reference/nx-cloud-cli#npx-nxcloud-startcirun
            # Uncomment this line to enable task distribution
            # - npx nx start-ci-run --distribute-on="3 linux-medium-js" --stop-agents-after="build"

            - npm ci --legacy-peer-deps

            # Prepend any command with "nx-cloud record --" to record its logs to Nx Cloud
            # npx nx-cloud record -- echo Hello World
            - npx nx run-many -t lint test build typecheck
            # Nx Cloud recommends fixes for failures to help you get CI green faster. Learn more: https://nx.dev/ci/features/self-healing-ci

          after-script:
            - npx nx fix-ci

  branches:
    main:
      - step:
          name: 'Build and test affected apps on "main" branch changes'
          script:
            - export NX_BRANCH=$BITBUCKET_BRANCH
            # This enables task distribution via Nx Cloud
            # Run this command as early as possible, before dependencies are installed
            # Learn more at https://nx.dev/ci/reference/nx-cloud-cli#npx-nxcloud-startcirun
            - npx nx start-ci-run --distribute-on="3 linux-medium-js" --stop-agents-after="build"

            - npm ci --legacy-peer-deps

            # Prepend any command with "nx-cloud record --" to record its logs to Nx Cloud
            # - npx nx-cloud record -- echo Hello World
            - npx nx run-many -t lint test build typecheck
